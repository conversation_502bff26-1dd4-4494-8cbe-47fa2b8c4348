// src/components/Grader.jsx
import React, { useState } from "react";
import { <PERSON>lipboard<PERSON><PERSON>, Calculator } from "lucide-react";

const Grader = ({ entries }) => {
  const [selectedEntry, setSelectedEntry] = useState(null);
  const [gradingData, setGradingData] = useState({
    unripe: "",
    underRipe: "",
    waste: "",
    longStalk: "",
  });

  const handleGradingSubmit = async () => {
    if (!selectedEntry) return;

    try {
      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL || "http://localhost:5000"
        }/api/entries/${selectedEntry._id}/grade`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(gradingData),
        }
      );

      if (response.ok) {
        setSelectedEntry(null);
        setGradingData({ unripe: "", underRipe: "", waste: "", longStalk: "" });
      } else {
        const error = await response.json();
        alert(error.message);
      }
    } catch (error) {
      console.error("Error submitting grade:", error);
    }
  };

  const calculateTotal = () => {
    const unripe = parseInt(gradingData.unripe) || 0;
    const underRipe = parseInt(gradingData.underRipe) || 0;
    const waste = parseInt(gradingData.waste) || 0;
    const longStalk = parseInt(gradingData.longStalk) || 0;

    return unripe + underRipe + waste + longStalk;
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "Complete":
        return <span className="status-badge status-complete">Complete</span>;
      case "Weight Added":
        return (
          <span className="status-badge status-weight-added">Weight Added</span>
        );
      case "Grading":
        return <span className="status-badge status-grading">Grading</span>;
      case "Pending":
        return <span className="status-badge status-pending">Pending</span>;
      default:
        return <span className="status-badge status-pending">{status}</span>;
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900">Grader Dashboard</h2>

      {/* Grading Form */}
      {selectedEntry && (
        <div className="card">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <ClipboardList className="w-5 h-5 text-orange-600" />
            Grading Details for {selectedEntry.farmerCode}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Unripe
              </label>
              <input
                type="number"
                className="form-input w-full"
                value={gradingData.unripe}
                onChange={(e) =>
                  setGradingData({ ...gradingData, unripe: e.target.value })
                }
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Under Ripe
              </label>
              <input
                type="number"
                className="form-input w-full"
                value={gradingData.underRipe}
                onChange={(e) =>
                  setGradingData({ ...gradingData, underRipe: e.target.value })
                }
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Waste
              </label>
              <input
                type="number"
                className="form-input w-full"
                value={gradingData.waste}
                onChange={(e) =>
                  setGradingData({ ...gradingData, waste: e.target.value })
                }
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Long Stalk
              </label>
              <input
                type="number"
                className="form-input w-full"
                value={gradingData.longStalk}
                onChange={(e) =>
                  setGradingData({ ...gradingData, longStalk: e.target.value })
                }
              />
            </div>
          </div>

          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Calculator className="w-5 h-5 text-gray-600" />
              <span className="text-lg font-semibold text-gray-900">
                Total: {calculateTotal()}
              </span>
            </div>
          </div>

          <div className="flex gap-3">
            <button onClick={handleGradingSubmit} className="btn-primary">
              Submit Grading
            </button>
            <button
              onClick={() => {
                setSelectedEntry(null);
                setGradingData({
                  unripe: "",
                  underRipe: "",
                  waste: "",
                  longStalk: "",
                });
              }}
              className="btn-secondary"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Grading Details Table */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-6">Grading Details</h3>
        <div className="overflow-x-auto">
          <table className="table-modern">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Farmer Code</th>
                <th>Unripe</th>
                <th>Under Ripe</th>
                <th>Waste</th>
                <th>Long Stalk</th>
                <th>Total</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {entries.map((entry, index) => {
                const total =
                  (entry.unripe || 0) +
                  (entry.underRipe || 0) +
                  (entry.waste || 0) +
                  (entry.longStalk || 0);

                return (
                  <tr key={entry._id}>
                    <td className="text-center font-medium text-gray-600">
                      {index + 1}
                    </td>
                    <td className="font-medium">{entry.farmerCode}</td>
                    <td className="text-center">{entry.unripe || "-"}</td>
                    <td className="text-center">{entry.underRipe || "-"}</td>
                    <td className="text-center">{entry.waste || "-"}</td>
                    <td className="text-center">{entry.longStalk || "-"}</td>
                    <td className="text-center font-medium">{total || "-"}</td>
                    <td>{getStatusBadge(entry.status)}</td>
                    <td>
                      <button
                        onClick={() => {
                          setSelectedEntry(entry);
                          setGradingData({
                            unripe: entry.unripe || "",
                            underRipe: entry.underRipe || "",
                            waste: entry.waste || "",
                            longStalk: entry.longStalk || "",
                          });
                        }}
                        className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                      >
                        {entry.unripe ? "Edit" : "Grade"}
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Grader;
