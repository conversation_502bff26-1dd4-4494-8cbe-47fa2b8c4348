// src/components/Dashboard.jsx
import React, { useState } from "react";
import {
  Package,
  CheckCircle,
  Weight,
  Clock,
  Search,
  Download,
} from "lucide-react";

const Dashboard = ({ entries, exportToExcel }) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredEntries = entries.filter((entry) =>
    entry.farmerCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const statusCounts = {
    Pending: entries.filter((entry) => entry.status === "Pending").length,
    Grading: entries.filter((entry) => entry.status === "Grading").length,
    "Weight Added": entries.filter((entry) => entry.status === "Weight Added")
      .length,
    Complete: entries.filter((entry) => entry.status === "Complete").length,
  };

  const totalWeight = entries.reduce(
    (sum, entry) => sum + (entry.weight || 0),
    0
  );
  const completedCount = statusCounts.Complete;
  const inProgressCount = entries.length - completedCount;

  const getStatusBadge = (status) => {
    switch (status) {
      case "Complete":
        return <span className="status-badge status-complete">Complete</span>;
      case "Weight Added":
        return (
          <span className="status-badge status-weight-added">Weight Added</span>
        );
      case "Grading":
        return <span className="status-badge status-grading">Grading</span>;
      case "Pending":
        return <span className="status-badge status-pending">Pending</span>;
      default:
        return <span className="status-badge status-pending">{status}</span>;
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">
        Dashboard Overview
      </h2>

      {/* Modern Metric Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Entries</p>
              <p className="text-3xl font-bold text-gray-900">
                {entries.length}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-3xl font-bold text-gray-900">
                {completedCount}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Weight</p>
              <p className="text-3xl font-bold text-gray-900">
                {totalWeight.toFixed(1)} kg
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Weight className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">In Progress</p>
              <p className="text-3xl font-bold text-gray-900">
                {inProgressCount}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Status Distribution */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-4">Status Distribution</h3>
        <div className="flex flex-wrap gap-3">
          {Object.entries(statusCounts).map(([status, count]) => (
            <div key={status} className="flex items-center gap-2">
              {getStatusBadge(status)}
              <span className="text-sm text-gray-600">{count}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Farmer Entries Table */}
      <div className="card">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold">Farmer Entries</h3>
          <div className="flex gap-3">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search by farmer code..."
                className="form-input pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <button
              onClick={exportToExcel}
              className="btn-primary flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Export Excel
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="table-modern">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Farmer Code</th>
                <th>Unripe</th>
                <th>Under Ripe</th>
                <th>Waste</th>
                <th>Long Stalk</th>
                <th>Weight (kg)</th>
                <th>Cluster</th>
                <th>Status</th>
                <th>Created</th>
              </tr>
            </thead>
            <tbody>
              {filteredEntries.map((entry, index) => (
                <tr key={entry._id}>
                  <td className="text-center font-medium text-gray-600">
                    {index + 1}
                  </td>
                  <td className="font-medium">{entry.farmerCode}</td>
                  <td className="text-center">{entry.unripe || "-"}</td>
                  <td className="text-center">{entry.underRipe || "-"}</td>
                  <td className="text-center">{entry.waste || "-"}</td>
                  <td className="text-center">{entry.longStalk || "-"}</td>
                  <td>{entry.weight ? `${entry.weight} kg` : "-"}</td>
                  <td>{entry.cluster || "-"}</td>
                  <td>{getStatusBadge(entry.status)}</td>
                  <td className="text-gray-500">
                    {new Date(entry.createdAt).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
