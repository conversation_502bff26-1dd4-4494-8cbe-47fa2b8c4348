// Test component to verify Field Assistant is working
import { useState } from "react";
import { MapPin } from "lucide-react";

const TestFieldAssistant = () => {
  const [testEntries] = useState([
    {
      _id: "1",
      farmerCode: "PF001",
      weight: 120,
      unripe: 25,
      underRipe: 5,
      waste: 5,
      longStalk: 3,
      cluster: "C1",
      status: "Complete",
      createdAt: new Date().toISOString()
    },
    {
      _id: "2",
      farmerCode: "PF002",
      weight: 145,
      unripe: 30,
      underRipe: 20,
      waste: 8,
      longStalk: 2,
      cluster: null,
      status: "Weight Added",
      createdAt: new Date().toISOString()
    }
  ]);

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Field Assistant Test</h1>
        
        {/* Test Icon */}
        <div className="mb-4">
          <MapPin className="w-6 h-6 text-green-600" />
          <span className="ml-2">Icon Test: MapPin should appear</span>
        </div>

        {/* Test Data */}
        <div className="bg-white p-4 rounded-lg shadow mb-6">
          <h2 className="font-semibold mb-2">Test Data:</h2>
          <pre className="text-sm bg-gray-100 p-2 rounded">
            {JSON.stringify(testEntries, null, 2)}
          </pre>
        </div>

        {/* Import FieldAssistant and test */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="font-semibold mb-4">Field Assistant Component:</h2>
          <p className="text-sm text-gray-600 mb-4">
            If you see this, the component is loading. Check console for any errors.
          </p>
          
          {/* Simple version of Field Assistant */}
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <MapPin className="w-6 h-6 text-green-600" />
              <h2 className="text-2xl font-bold text-gray-900">
                Field Assistant Dashboard
              </h2>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold mb-6">Cluster Assignment</h3>
              <div className="overflow-x-auto">
                <table className="table-modern">
                  <thead>
                    <tr>
                      <th>Farmer Code</th>
                      <th>Weight (kg)</th>
                      <th>Grading Summary</th>
                      <th>Cluster</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {testEntries.map((entry) => (
                      <tr key={entry._id}>
                        <td className="font-medium">{entry.farmerCode}</td>
                        <td>{entry.weight ? `${entry.weight} kg` : "-"}</td>
                        <td>
                          <span className="grading-summary">
                            {entry.unripe || 0} | {entry.underRipe || 0} | {entry.waste || 0} | {entry.longStalk || 0}
                          </span>
                        </td>
                        <td>{entry.cluster || "Not assigned"}</td>
                        <td>
                          <span className={`status-badge ${
                            entry.status === "Complete" ? "status-complete" : "status-weight-added"
                          }`}>
                            {entry.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestFieldAssistant;
