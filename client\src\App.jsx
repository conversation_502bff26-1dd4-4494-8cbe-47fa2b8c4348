// src/App.jsx
import React, { useState, useEffect } from "react";
import { io } from "socket.io-client";
import { Leaf } from "lucide-react";
import Dashboard from "./components/Dashboard";
import Operator from "./components/Operator";
import Grader from "./components/Grader";
import FieldAssistant from "./components/FieldAssistant";

const socket = io(import.meta.env.VITE_API_URL || "http://localhost:5000");

function App() {
  const [activeTab, setActiveTab] = useState("dashboard");
  const [entries, setEntries] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchEntries();

    socket.on("entryUpdated", (entry) => {
      setEntries((prev) => {
        const index = prev.findIndex((e) => e._id === entry._id);
        if (index !== -1) {
          const newEntries = [...prev];
          newEntries[index] = entry;
          return newEntries;
        } else {
          return [entry, ...prev];
        }
      });
    });

    socket.on("entryDeleted", (id) => {
      setEntries((prev) => prev.filter((entry) => entry._id !== id));
    });

    return () => {
      socket.off("entryUpdated");
      socket.off("entryDeleted");
    };
  }, []);

  const fetchEntries = async () => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL || "http://localhost:5000"}/api/entries`
      );
      const data = await response.json();
      setEntries(data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching entries:", error);
      setLoading(false);
    }
  };

  const exportToExcel = async () => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL || "http://localhost:5000"}/api/export`
      );
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "palm_fruit_collection.xlsx";
      document.body.appendChild(a);
      a.click();
      a.remove();
    } catch (error) {
      console.error("Error exporting to Excel:", error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        Loading...
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Modern Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-3 sm:gap-4">
              {/* Circular Logo */}
              <div className="logo-circle flex-shrink-0">
                <Leaf className="w-4 h-4 sm:w-6 sm:h-6" />
              </div>

              {/* Brand Text */}
              <div className="text-center">
                <h1 className="text-lg sm:text-2xl font-bold bg-gradient-to-r from-green-600 to-green-500 bg-clip-text text-transparent">
                  Palm Fruit Collection
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 mt-1 hidden sm:block">
                  Streamlined workflow management for palm fruit processing
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Modern Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="flex justify-center">
            <div className="flex space-x-2 sm:space-x-8 overflow-x-auto min-w-max sm:min-w-0">
              <button
                className={`nav-tab ${
                  activeTab === "dashboard" ? "active" : ""
                }`}
                onClick={() => setActiveTab("dashboard")}
              >
                Dashboard
              </button>
              <button
                className={`nav-tab ${
                  activeTab === "operator" ? "active" : ""
                }`}
                onClick={() => setActiveTab("operator")}
              >
                Operator
              </button>
              <button
                className={`nav-tab ${activeTab === "grader" ? "active" : ""}`}
                onClick={() => setActiveTab("grader")}
              >
                Grader
              </button>
              <button
                className={`nav-tab ${
                  activeTab === "fieldAssistant" ? "active" : ""
                }`}
                onClick={() => setActiveTab("fieldAssistant")}
              >
                <span className="hidden sm:inline">Field Assistant</span>
                <span className="sm:hidden">Field</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div className="fade-in">
          {activeTab === "dashboard" && (
            <Dashboard entries={entries} exportToExcel={exportToExcel} />
          )}
          {activeTab === "operator" && <Operator entries={entries} />}
          {activeTab === "grader" && <Grader entries={entries} />}
          {activeTab === "fieldAssistant" && (
            <FieldAssistant entries={entries} />
          )}
        </div>
      </main>
    </div>
  );
}

export default App;
