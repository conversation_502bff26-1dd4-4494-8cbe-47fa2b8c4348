// src/components/FieldAssistant.jsx
import React, { useState } from "react";
import { MapPin, Calculator, BarChart3, X } from "lucide-react";

const FieldAssistant = ({ entries }) => {
  const [selectedEntry, setSelectedEntry] = useState(null);
  const [cluster, setCluster] = useState("");
  const [showTotalSummary, setShowTotalSummary] = useState(false);

  // Debug logging (can be removed in production)
  // console.log("FieldAssistant rendered with entries:", entries);

  // Ensure entries is an array and sort by creation date (oldest first) for consistent serial numbers
  const safeEntries = Array.isArray(entries)
    ? [...entries].sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
    : [];

  const handleClusterSubmit = async () => {
    if (!selectedEntry || !cluster.trim()) {
      alert("Please enter a cluster name");
      return;
    }

    try {
      console.log(
        "Submitting cluster:",
        cluster,
        "for entry:",
        selectedEntry._id
      );

      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL || "http://localhost:5000"
        }/api/entries/${selectedEntry._id}/cluster`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ cluster: cluster.trim() }),
        }
      );

      if (response.ok) {
        const updatedEntry = await response.json();
        console.log("Cluster assigned successfully:", updatedEntry);
        setSelectedEntry(null);
        setCluster("");
        // The socket.io connection should automatically update the entries
      } else {
        const error = await response.json();
        console.error("Server error:", error);
        alert(error.message || "Failed to assign cluster");
      }
    } catch (error) {
      console.error("Error submitting cluster:", error);
      alert("Network error: Failed to assign cluster");
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "Complete":
        return <span className="status-badge status-complete">Complete</span>;
      case "Weight Added":
        return (
          <span className="status-badge status-weight-added">Weight Added</span>
        );
      case "Grading":
        return <span className="status-badge status-grading">Grading</span>;
      case "Pending":
        return <span className="status-badge status-pending">Pending</span>;
      default:
        return <span className="status-badge status-pending">{status}</span>;
    }
  };

  const formatTimestamp = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      month: "numeric",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
      second: "2-digit",
      hour12: true,
    });
  };

  const calculateClusterSummary = () => {
    const clusterSummary = {};

    safeEntries.forEach((entry) => {
      const clusterName = entry.cluster || "Unassigned";

      if (!clusterSummary[clusterName]) {
        clusterSummary[clusterName] = {
          totalEntries: 0,
          totalWeight: 0,
          totalUnripe: 0,
          totalUnderRipe: 0,
          totalWaste: 0,
          totalLongStalk: 0,
          completedEntries: 0,
          entries: [],
        };
      }

      const cluster = clusterSummary[clusterName];
      cluster.totalEntries += 1;
      cluster.totalWeight += parseFloat(entry.weight) || 0;
      cluster.totalUnripe += parseInt(entry.unripe) || 0;
      cluster.totalUnderRipe += parseInt(entry.underRipe) || 0;
      cluster.totalWaste += parseInt(entry.waste) || 0;
      cluster.totalLongStalk += parseInt(entry.longStalk) || 0;

      if (entry.status === "Complete") {
        cluster.completedEntries += 1;
      }

      cluster.entries.push(entry);
    });

    return clusterSummary;
  };

  return (
    <div className="space-y-6">
      {/* Header with Location Icon */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <MapPin className="w-6 h-6 text-green-600 flex-shrink-0" />
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
            Field Assistant Dashboard
          </h2>
        </div>
        {safeEntries.length > 0 && (
          <button
            onClick={() => setShowTotalSummary(true)}
            className="btn-primary flex items-center gap-2"
          >
            <Calculator className="w-4 h-4" />
            Execute Total
          </button>
        )}
      </div>

      {/* Cluster Assignment Form */}
      {selectedEntry && (
        <div className="card">
          <h3 className="text-base sm:text-lg font-semibold mb-4">
            Cluster Assignment for {selectedEntry.farmerCode}
          </h3>
          <div className="responsive-flex">
            <input
              type="text"
              placeholder="Enter cluster ("
              className="form-input flex-1 min-w-0"
              value={cluster}
              onChange={(e) => setCluster(e.target.value)}
            />
            <div className="flex gap-2 sm:gap-3">
              <button
                onClick={handleClusterSubmit}
                className="btn-primary whitespace-nowrap"
              >
                Assign Cluster
              </button>
              <button
                onClick={() => {
                  setSelectedEntry(null);
                  setCluster("");
                }}
                className="btn-secondary whitespace-nowrap"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Cluster Assignment Table */}
      <div className="card">
        <h3 className="text-base sm:text-lg font-semibold mb-4 sm:mb-6">
          Cluster Assignment
        </h3>

        {/* Empty State */}
        {safeEntries.length === 0 && (
          <div className="text-center py-8">
            <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No entries available</p>
          </div>
        )}

        {/* Mobile Card View */}
        <div className="block sm:hidden space-y-4">
          {safeEntries.map((entry, index) => {
            const gradingSummary =
              entry.unripe ||
              entry.underRipe ||
              entry.waste ||
              entry.longStalk ? (
                <span className="grading-summary">
                  {entry.unripe || 0} | {entry.underRipe || 0} |{" "}
                  {entry.waste || 0} | {entry.longStalk || 0}
                </span>
              ) : (
                "-"
              );

            return (
              <div
                key={entry._id}
                className="bg-gray-50 rounded-lg p-4 space-y-2"
              >
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-500">
                      #{index + 1}
                    </span>
                    <span className="font-medium text-gray-900">
                      {entry.farmerCode}
                    </span>
                  </div>
                  {getStatusBadge(entry.status)}
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-gray-500">Weight:</span>
                    <span className="ml-1">
                      {entry.weight ? `${entry.weight} kg` : "-"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Cluster:</span>
                    <span className="ml-1">
                      {entry.cluster ? (
                        <span className="font-medium text-gray-900">
                          {entry.cluster}
                        </span>
                      ) : (
                        <button
                          onClick={() => setSelectedEntry(entry)}
                          className="text-blue-600 hover:text-blue-800 font-medium text-xs"
                        >
                          Enter cluster (
                        </button>
                      )}
                    </span>
                  </div>
                </div>
                <div className="text-xs space-y-1">
                  <div>
                    <span className="text-gray-500">Grading:</span>
                    <span className="ml-1">{gradingSummary}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Time:</span>
                    <span className="ml-1 text-gray-500">
                      {formatTimestamp(entry.createdAt)}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Desktop Table View */}
        <div className="hidden sm:block overflow-x-auto">
          <table className="table-modern">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Farmer Code</th>
                <th className="mobile-hidden">Weight (kg)</th>
                <th className="mobile-hidden">Grading Summary</th>
                <th>Cluster</th>
                <th>Status</th>
                <th className="mobile-hidden">Timestamp</th>
              </tr>
            </thead>
            <tbody>
              {safeEntries.map((entry, index) => {
                const gradingSummary =
                  entry.unripe ||
                  entry.underRipe ||
                  entry.waste ||
                  entry.longStalk ? (
                    <span className="grading-summary">
                      {entry.unripe || 0} | {entry.underRipe || 0} |{" "}
                      {entry.waste || 0} | {entry.longStalk || 0}
                    </span>
                  ) : (
                    "-"
                  );

                return (
                  <tr key={entry._id}>
                    <td className="text-center font-medium text-gray-600">
                      {index + 1}
                    </td>
                    <td className="font-medium">{entry.farmerCode}</td>
                    <td className="mobile-hidden">
                      {entry.weight ? `${entry.weight} kg` : "-"}
                    </td>
                    <td className="mobile-hidden">{gradingSummary}</td>
                    <td>
                      {entry.cluster ? (
                        <span className="font-medium text-gray-900">
                          {entry.cluster}
                        </span>
                      ) : (
                        <button
                          onClick={() => setSelectedEntry(entry)}
                          className="text-blue-600 hover:text-blue-800 font-medium text-sm whitespace-nowrap"
                        >
                          Enter cluster (
                        </button>
                      )}
                    </td>
                    <td>{getStatusBadge(entry.status)}</td>
                    <td className="text-gray-500 text-sm mobile-hidden">
                      {formatTimestamp(entry.createdAt)}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Cluster Summary Modal */}
      {showTotalSummary && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    Cluster-wise Summary Report
                  </h3>
                  <p className="text-sm text-gray-600">
                    Total entries: {safeEntries.length}
                  </p>
                </div>
              </div>
              <button
                onClick={() => setShowTotalSummary(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {(() => {
                const clusterSummary = calculateClusterSummary();
                const clusters = Object.keys(clusterSummary).sort();

                return (
                  <div className="space-y-6">
                    {clusters.map((clusterName) => {
                      const cluster = clusterSummary[clusterName];
                      const completionRate =
                        cluster.totalEntries > 0
                          ? (
                              (cluster.completedEntries /
                                cluster.totalEntries) *
                              100
                            ).toFixed(1)
                          : 0;

                      return (
                        <div
                          key={clusterName}
                          className="bg-gray-50 rounded-lg p-6"
                        >
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="text-lg font-semibold text-gray-900">
                              {clusterName === "Unassigned"
                                ? "🔄 Unassigned"
                                : `📍 ${clusterName}`}
                            </h4>
                            <div className="text-sm text-gray-600">
                              {cluster.completedEntries}/{cluster.totalEntries}{" "}
                              Complete ({completionRate}%)
                            </div>
                          </div>

                          {/* Summary Cards */}
                          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-4">
                            <div className="bg-white p-4 rounded-lg border">
                              <div className="text-sm text-gray-600">
                                Total Entries
                              </div>
                              <div className="text-2xl font-bold text-blue-600">
                                {cluster.totalEntries}
                              </div>
                            </div>
                            <div className="bg-white p-4 rounded-lg border">
                              <div className="text-sm text-gray-600">
                                Total Weight
                              </div>
                              <div className="text-2xl font-bold text-green-600">
                                {cluster.totalWeight.toFixed(1)} kg
                              </div>
                            </div>
                            <div className="bg-white p-4 rounded-lg border">
                              <div className="text-sm text-gray-600">
                                Unripe
                              </div>
                              <div className="text-2xl font-bold text-red-600">
                                {cluster.totalUnripe}
                              </div>
                            </div>
                            <div className="bg-white p-4 rounded-lg border">
                              <div className="text-sm text-gray-600">
                                Under Ripe
                              </div>
                              <div className="text-2xl font-bold text-orange-600">
                                {cluster.totalUnderRipe}
                              </div>
                            </div>
                            <div className="bg-white p-4 rounded-lg border">
                              <div className="text-sm text-gray-600">Waste</div>
                              <div className="text-2xl font-bold text-gray-600">
                                {cluster.totalWaste}
                              </div>
                            </div>
                            <div className="bg-white p-4 rounded-lg border">
                              <div className="text-sm text-gray-600">
                                Long Stalk
                              </div>
                              <div className="text-2xl font-bold text-purple-600">
                                {cluster.totalLongStalk}
                              </div>
                            </div>
                          </div>

                          {/* Farmer Codes */}
                          <div className="bg-white p-4 rounded-lg border">
                            <div className="text-sm text-gray-600 mb-2">
                              Farmer Codes:
                            </div>
                            <div className="flex flex-wrap gap-2">
                              {cluster.entries.map((entry) => (
                                <span
                                  key={entry._id}
                                  className={`px-2 py-1 rounded text-xs font-medium ${
                                    entry.status === "Complete"
                                      ? "bg-green-100 text-green-800"
                                      : "bg-gray-100 text-gray-800"
                                  }`}
                                >
                                  {entry.farmerCode}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      );
                    })}

                    {/* Overall Summary */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-blue-900 mb-4">
                        📊 Overall Summary
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {clusters.length}
                          </div>
                          <div className="text-sm text-blue-700">
                            Total Clusters
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {Object.values(clusterSummary)
                              .reduce(
                                (sum, cluster) => sum + cluster.totalWeight,
                                0
                              )
                              .toFixed(1)}{" "}
                            kg
                          </div>
                          <div className="text-sm text-green-700">
                            Total Weight
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">
                            {Object.values(clusterSummary).reduce(
                              (sum, cluster) => sum + cluster.completedEntries,
                              0
                            )}
                          </div>
                          <div className="text-sm text-purple-700">
                            Completed Entries
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">
                            {(
                              (Object.values(clusterSummary).reduce(
                                (sum, cluster) =>
                                  sum + cluster.completedEntries,
                                0
                              ) /
                                safeEntries.length) *
                              100
                            ).toFixed(1)}
                            %
                          </div>
                          <div className="text-sm text-orange-700">
                            Completion Rate
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end p-6 border-t border-gray-200">
              <button
                onClick={() => setShowTotalSummary(false)}
                className="btn-secondary"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FieldAssistant;
