// src/components/FieldAssistant.jsx
import React, { useState } from "react";
import { MapPin } from "lucide-react";

const FieldAssistant = ({ entries }) => {
  const [selectedEntry, setSelectedEntry] = useState(null);
  const [cluster, setCluster] = useState("");

  // Debug logging
  console.log("FieldAssistant rendered with entries:", entries);

  // Ensure entries is an array
  const safeEntries = Array.isArray(entries) ? entries : [];

  const handleClusterSubmit = async () => {
    if (!selectedEntry || !cluster.trim()) {
      alert("Please enter a cluster name");
      return;
    }

    try {
      console.log(
        "Submitting cluster:",
        cluster,
        "for entry:",
        selectedEntry._id
      );

      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL || "http://localhost:5000"
        }/api/entries/${selectedEntry._id}/cluster`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ cluster: cluster.trim() }),
        }
      );

      if (response.ok) {
        const updatedEntry = await response.json();
        console.log("Cluster assigned successfully:", updatedEntry);
        setSelectedEntry(null);
        setCluster("");
        // The socket.io connection should automatically update the entries
      } else {
        const error = await response.json();
        console.error("Server error:", error);
        alert(error.message || "Failed to assign cluster");
      }
    } catch (error) {
      console.error("Error submitting cluster:", error);
      alert("Network error: Failed to assign cluster");
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "Complete":
        return <span className="status-badge status-complete">Complete</span>;
      case "Weight Added":
        return (
          <span className="status-badge status-weight-added">Weight Added</span>
        );
      case "Grading":
        return <span className="status-badge status-grading">Grading</span>;
      case "Pending":
        return <span className="status-badge status-pending">Pending</span>;
      default:
        return <span className="status-badge status-pending">{status}</span>;
    }
  };

  const formatTimestamp = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      month: "1/2-digit",
      day: "1/2-digit",
      year: "numeric",
      hour: "1/2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: true,
    });
  };

  return (
    <div className="space-y-6">
      {/* Header with Location Icon */}
      <div className="flex items-center gap-3 mb-6">
        <MapPin className="w-6 h-6 text-green-600 flex-shrink-0" />
        <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
          Field Assistant Dashboard
        </h2>
      </div>

      {/* Cluster Assignment Form */}
      {selectedEntry && (
        <div className="card">
          <h3 className="text-base sm:text-lg font-semibold mb-4">
            Cluster Assignment for {selectedEntry.farmerCode}
          </h3>
          <div className="responsive-flex">
            <input
              type="text"
              placeholder="Enter cluster ("
              className="form-input flex-1 min-w-0"
              value={cluster}
              onChange={(e) => setCluster(e.target.value)}
            />
            <div className="flex gap-2 sm:gap-3">
              <button
                onClick={handleClusterSubmit}
                className="btn-primary whitespace-nowrap"
              >
                Assign Cluster
              </button>
              <button
                onClick={() => {
                  setSelectedEntry(null);
                  setCluster("");
                }}
                className="btn-secondary whitespace-nowrap"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Cluster Assignment Table */}
      <div className="card">
        <h3 className="text-base sm:text-lg font-semibold mb-4 sm:mb-6">
          Cluster Assignment
        </h3>

        {/* Empty State */}
        {safeEntries.length === 0 && (
          <div className="text-center py-8">
            <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No entries available</p>
          </div>
        )}

        {/* Mobile Card View */}
        <div className="block sm:hidden space-y-4">
          {safeEntries.map((entry) => {
            const gradingSummary =
              entry.unripe ||
              entry.underRipe ||
              entry.waste ||
              entry.longStalk ? (
                <span className="grading-summary">
                  {entry.unripe || 0} | {entry.underRipe || 0} |{" "}
                  {entry.waste || 0} | {entry.longStalk || 0}
                </span>
              ) : (
                "-"
              );

            return (
              <div
                key={entry._id}
                className="bg-gray-50 rounded-lg p-4 space-y-2"
              >
                <div className="flex justify-between items-start">
                  <span className="font-medium text-gray-900">
                    {entry.farmerCode}
                  </span>
                  {getStatusBadge(entry.status)}
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-gray-500">Weight:</span>
                    <span className="ml-1">
                      {entry.weight ? `${entry.weight} kg` : "-"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Cluster:</span>
                    <span className="ml-1">
                      {entry.cluster ? (
                        <span className="font-medium text-gray-900">
                          {entry.cluster}
                        </span>
                      ) : (
                        <button
                          onClick={() => setSelectedEntry(entry)}
                          className="text-blue-600 hover:text-blue-800 font-medium text-xs"
                        >
                          Enter cluster (
                        </button>
                      )}
                    </span>
                  </div>
                </div>
                <div className="text-xs space-y-1">
                  <div>
                    <span className="text-gray-500">Grading:</span>
                    <span className="ml-1">{gradingSummary}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Time:</span>
                    <span className="ml-1 text-gray-500">
                      {formatTimestamp(entry.createdAt)}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Desktop Table View */}
        <div className="hidden sm:block overflow-x-auto">
          <table className="table-modern">
            <thead>
              <tr>
                <th>Farmer Code</th>
                <th className="mobile-hidden">Weight (kg)</th>
                <th className="mobile-hidden">Grading Summary</th>
                <th>Cluster</th>
                <th>Status</th>
                <th className="mobile-hidden">Timestamp</th>
              </tr>
            </thead>
            <tbody>
              {safeEntries.map((entry) => {
                const gradingSummary =
                  entry.unripe ||
                  entry.underRipe ||
                  entry.waste ||
                  entry.longStalk ? (
                    <span className="grading-summary">
                      {entry.unripe || 0} | {entry.underRipe || 0} |{" "}
                      {entry.waste || 0} | {entry.longStalk || 0}
                    </span>
                  ) : (
                    "-"
                  );

                return (
                  <tr key={entry._id}>
                    <td className="font-medium">{entry.farmerCode}</td>
                    <td className="mobile-hidden">
                      {entry.weight ? `${entry.weight} kg` : "-"}
                    </td>
                    <td className="mobile-hidden">{gradingSummary}</td>
                    <td>
                      {entry.cluster ? (
                        <span className="font-medium text-gray-900">
                          {entry.cluster}
                        </span>
                      ) : (
                        <button
                          onClick={() => setSelectedEntry(entry)}
                          className="text-blue-600 hover:text-blue-800 font-medium text-sm whitespace-nowrap"
                        >
                          Enter cluster (
                        </button>
                      )}
                    </td>
                    <td>{getStatusBadge(entry.status)}</td>
                    <td className="text-gray-500 text-sm mobile-hidden">
                      {formatTimestamp(entry.createdAt)}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default FieldAssistant;
