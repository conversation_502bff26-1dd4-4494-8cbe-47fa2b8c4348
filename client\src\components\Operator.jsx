// src/components/Operator.jsx
import React, { useState } from "react";
import { Plus, Scale, Trash2, Alert<PERSON><PERSON>gle, X } from "lucide-react";

const Operator = ({ entries }) => {
  const [farmerCode, setFarmerCode] = useState("");
  const [selectedEntry, setSelectedEntry] = useState(null);
  const [weight, setWeight] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Sort entries by creation date (oldest first) for consistent serial numbers
  const sortedEntries = [...entries].sort(
    (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
  );

  const handleAddEntry = async () => {
    if (!farmerCode) return;

    try {
      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL || "http://localhost:5000"
        }/api/entries`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ farmerCode }),
        }
      );

      if (response.ok) {
        setFarmerCode("");
      } else {
        const error = await response.json();
        alert(error.message);
      }
    } catch (error) {
      console.error("Error adding entry:", error);
    }
  };

  const handleAddWeight = async (entry) => {
    if (!weight || isNaN(weight)) return;

    try {
      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL || "http://localhost:5000"
        }/api/entries/${entry._id}/weight`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ weight: parseFloat(weight) }),
        }
      );

      if (response.ok) {
        setWeight("");
        setSelectedEntry(null);
      } else {
        const error = await response.json();
        alert(error.message);
      }
    } catch (error) {
      console.error("Error adding weight:", error);
    }
  };

  const handleDeleteAll = async () => {
    setIsDeleting(true);
    try {
      // Delete all entries one by one
      const deletePromises = sortedEntries.map((entry) =>
        fetch(
          `${
            import.meta.env.VITE_API_URL || "http://localhost:5000"
          }/api/entries/${entry._id}`,
          { method: "DELETE" }
        )
      );

      await Promise.all(deletePromises);

      setShowDeleteModal(false);
      setIsDeleting(false);

      // Show success message
      alert("All data has been successfully deleted!");
    } catch (error) {
      console.error("Error deleting all entries:", error);
      alert("Error occurred while deleting data. Please try again.");
      setIsDeleting(false);
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "Complete":
        return <span className="status-badge status-complete">Complete</span>;
      case "Weight Added":
        return (
          <span className="status-badge status-weight-added">Weight Added</span>
        );
      case "Grading":
        return <span className="status-badge status-grading">Grading</span>;
      case "Pending":
        return <span className="status-badge status-pending">Pending</span>;
      default:
        return <span className="status-badge status-pending">{status}</span>;
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900">Operator Dashboard</h2>

      {/* Add New Farmer Code */}
      <div className="card">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Plus className="w-5 h-5 text-green-600" />
            Add New Farmer Code
          </h3>
          {sortedEntries.length > 0 && (
            <button
              onClick={() => setShowDeleteModal(true)}
              className="btn-danger flex items-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Delete All Data
            </button>
          )}
        </div>
        <div className="flex gap-3">
          <input
            type="text"
            placeholder="Enter farmer code (e.g., PF006)"
            className="form-input flex-1"
            value={farmerCode}
            onChange={(e) => setFarmerCode(e.target.value)}
          />
          <button
            onClick={handleAddEntry}
            className="btn-primary flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Entry
          </button>
        </div>
      </div>

      {/* Weight Entry */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Scale className="w-5 h-5 text-blue-600" />
          Weight Entry
        </h3>

        {selectedEntry && (
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-800 mb-3">
              Add Weight for {selectedEntry.farmerCode}
            </h4>
            <div className="flex gap-3">
              <input
                type="number"
                placeholder="Weight (kg)"
                className="form-input w-32"
                value={weight}
                onChange={(e) => setWeight(e.target.value)}
              />
              <button
                onClick={() => handleAddWeight(selectedEntry)}
                className="btn-primary"
              >
                Add Weight
              </button>
              <button
                onClick={() => setSelectedEntry(null)}
                className="btn-secondary"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="table-modern">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Farmer Code</th>
                <th>Weight (kg)</th>
                <th>Status</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedEntries.map((entry, index) => (
                <tr key={entry._id}>
                  <td className="text-center font-medium text-gray-600">
                    {index + 1}
                  </td>
                  <td className="font-medium">{entry.farmerCode}</td>
                  <td>{entry.weight ? `${entry.weight} kg` : "-"}</td>
                  <td>{getStatusBadge(entry.status)}</td>
                  <td className="text-gray-500">
                    {new Date(entry.createdAt).toLocaleDateString()}
                  </td>
                  <td>
                    {!entry.weight && (
                      <button
                        onClick={() => setSelectedEntry(entry)}
                        className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                      >
                        Add Weight
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Delete All Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Delete All Data
                </h3>
                <p className="text-sm text-gray-600">
                  This action cannot be undone
                </p>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-gray-700 mb-2">
                Are you sure you want to delete all farmer entries? This will
                permanently remove:
              </p>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                <li>
                  <strong>{sortedEntries.length}</strong> farmer entries
                </li>
                <li>All weight data</li>
                <li>All grading information</li>
                <li>All cluster assignments</li>
              </ul>
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-800 font-medium">
                  ⚠️ This action is permanent and cannot be reversed!
                </p>
              </div>
            </div>

            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="btn-secondary flex items-center gap-2"
                disabled={isDeleting}
              >
                <X className="w-4 h-4" />
                Cancel
              </button>
              <button
                onClick={handleDeleteAll}
                className="btn-danger flex items-center gap-2"
                disabled={isDeleting}
              >
                <Trash2 className="w-4 h-4" />
                {isDeleting ? "Deleting..." : "Delete All Data"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Operator;
