@import "tailwindcss";

/* Modern Design System for Palm Fruit Collection */
:root {
  /* Typography */
  font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
    sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Colors */
  --green-primary: #22c55e;
  --green-secondary: #16a34a;
  --green-dark: #15803d;
  --green-light: #dcfce7;

  /* Status Colors */
  --status-complete: #22c55e;
  --status-weight-added: #eab308;
  --status-grading: #f97316;
  --status-pending: #ef4444;

  /* Grading Colors */
  --grading-numbers: #dc2626;

  /* Neutral Colors */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Optimizations */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  background-color: var(--gray-50);
  color: var(--gray-900);
  min-height: 100vh;
}

/* Component Styles */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.metric-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200;
}

/* Status Badges */
.status-badge {
  @apply px-3 py-1 rounded-full text-xs font-medium inline-flex items-center gap-1;
}

.status-complete {
  @apply bg-green-100 text-green-800;
}

.status-weight-added {
  @apply bg-yellow-100 text-yellow-800;
}

.status-grading {
  @apply bg-orange-100 text-orange-800;
}

.status-pending {
  @apply bg-red-100 text-red-800;
}

/* Grading Summary */
.grading-summary {
  color: var(--grading-numbers);
  @apply font-medium;
}

/* Modern Button Styles */
.btn-primary {
  @apply bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200;
}

/* Form Styles */
.form-input {
  @apply border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors duration-200;
}

/* Table Styles */
.table-modern {
  @apply min-w-full bg-white rounded-lg overflow-hidden shadow-sm;
}

.table-modern th {
  @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;
}

.table-modern td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-100;
}

.table-modern tbody tr:hover {
  @apply bg-gray-50;
}

/* Logo Styles */
.logo-circle {
  @apply w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white;
}

/* Navigation Styles */
.nav-tab {
  @apply px-6 py-3 text-sm font-medium text-gray-600 hover:text-green-600 hover:bg-green-50 transition-colors duration-200 border-b-2 border-transparent;
}

.nav-tab.active {
  @apply text-green-600 border-green-600 bg-green-50;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design Improvements */
@media (max-width: 1024px) {
  .container {
    @apply px-4;
  }

  /* Header adjustments */
  header .container {
    @apply px-4 py-3;
  }

  header h1 {
    @apply text-xl;
  }

  header p {
    @apply text-xs;
  }
}

@media (max-width: 768px) {
  .metric-card {
    @apply p-4;
  }

  .card {
    @apply p-4;
  }

  /* Navigation improvements */
  .nav-tab {
    @apply px-3 py-2 text-sm;
  }

  /* Logo adjustments */
  .logo-circle {
    @apply w-10 h-10;
  }

  /* Table improvements */
  .table-modern {
    @apply text-sm;
  }

  .table-modern th,
  .table-modern td {
    @apply px-3 py-2;
  }

  /* Button improvements */
  .btn-primary,
  .btn-secondary,
  .btn-danger {
    @apply px-3 py-2 text-sm;
  }

  /* Form improvements */
  .form-input {
    @apply text-sm px-3 py-2;
  }

  /* Header responsive */
  header .flex {
    @apply flex-col gap-2;
  }

  header .text-center {
    @apply text-center;
  }

  /* Main content spacing */
  main {
    @apply px-4 py-6;
  }

  /* Grid adjustments */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
    @apply grid-cols-1 gap-4;
  }

  /* Status badges responsive */
  .status-badge {
    @apply text-xs px-2 py-1;
  }
}

@media (max-width: 640px) {
  /* Navigation for small screens */
  .nav-tab {
    @apply px-2 py-2 text-xs;
  }

  /* Metric cards for small screens */
  .metric-card .text-3xl {
    @apply text-2xl;
  }

  .metric-card {
    @apply p-3;
  }

  /* Table for small screens */
  .table-modern th,
  .table-modern td {
    @apply px-2 py-2 text-xs;
  }

  /* Form adjustments */
  .form-input {
    @apply text-xs px-2 py-2;
  }

  /* Button adjustments */
  .btn-primary,
  .btn-secondary,
  .btn-danger {
    @apply px-2 py-1 text-xs;
  }

  /* Header for mobile */
  header h1 {
    @apply text-lg;
  }

  header p {
    @apply text-xs;
  }

  .logo-circle {
    @apply w-8 h-8;
  }

  /* Card spacing */
  .card {
    @apply p-3;
  }

  /* Main content */
  main {
    @apply px-3 py-4;
  }

  /* Navigation scroll for mobile */
  nav .container > div {
    @apply overflow-x-auto;
  }

  nav .flex {
    @apply min-w-max;
  }
}

/* Additional responsive utilities */
.responsive-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
}

.responsive-flex {
  @apply flex flex-col sm:flex-row gap-3;
}

.mobile-hidden {
  @apply hidden sm:block;
}

.mobile-only {
  @apply block sm:hidden;
}
